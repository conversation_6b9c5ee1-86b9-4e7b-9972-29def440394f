// Test script for AI features
const { SpeechRecognizer } = require('./dist/speech/recognizer');
const { clipboard } = require('electron');

// Mock clipboard content for testing
const mockClipboard = {
    emailContent: "Hi there, we're interested in your premium consulting services for our upcoming project. We have a budget of $50,000 and need to start next month. Please let us know if you can accommodate this timeline and budget. Best regards, <PERSON>",
    
    messageContent: "Hey, can you help us with our marketing campaign? We need someone to design graphics and write copy for our social media posts. We can pay $2,000 for the whole project.",
    
    textToRewrite: "The quick brown fox jumps over the lazy dog. This is a sample sentence that we want to transform using various rewrite commands.",
    
    questionContext: "What are the main benefits of using renewable energy sources?"
};

async function testAIFeatures() {
    console.log('🤖 Testing AI Features for WhisperTyping Clone\n');
    
    const recognizer = new SpeechRecognizer();
    
    // Test 1: Write Mode with Clipboard
    console.log('1. 📝 Testing WRITE MODE with clipboard reference:');
    console.log('   Command: "Write an answer to the message in my clipboard, and tell them politely that their offer is not as compelling as competing offers"');
    
    // Simulate clipboard content
    const writeCommand = recognizer.detectAICommand(
        "Write an answer to the message in my clipboard, and tell them politely that their offer is not as compelling as competing offers"
    );
    
    if (writeCommand) {
        writeCommand.clipboardContent = mockClipboard.messageContent;
        const response = await recognizer.processWithAI("", writeCommand);
        console.log('   ✅ Detected as WRITE mode');
        console.log('   📋 Clipboard content:', mockClipboard.messageContent.substring(0, 60) + '...');
        console.log('   🤖 AI Response:', response.substring(0, 100) + '...\n');
    }
    
    // Test 2: Answer Mode
    console.log('2. ❓ Testing ANSWER MODE:');
    console.log('   Command: "Answer the question: How tall is the Eiffel tower?"');
    
    const answerCommand = recognizer.detectAICommand("Answer the question: How tall is the Eiffel tower?");
    if (answerCommand) {
        const response = await recognizer.processWithAI("", answerCommand);
        console.log('   ✅ Detected as ANSWER mode');
        console.log('   🤖 AI Response:', response + '\n');
    }
    
    // Test 3: Rewrite Mode - Portuguese
    console.log('3. ✏️ Testing REWRITE MODE (Portuguese):');
    console.log('   Command: "Rewrite this to Portuguese"');
    
    const rewriteCommand1 = recognizer.detectAICommand("Rewrite this to Portuguese");
    if (rewriteCommand1) {
        rewriteCommand1.clipboardContent = mockClipboard.textToRewrite;
        const response = await recognizer.processWithAI("", rewriteCommand1);
        console.log('   ✅ Detected as REWRITE mode');
        console.log('   📋 Original text:', mockClipboard.textToRewrite);
        console.log('   🤖 AI Response:', response + '\n');
    }
    
    // Test 4: Rewrite Mode - Bullet Points
    console.log('4. 📋 Testing REWRITE MODE (Bullet Points):');
    console.log('   Command: "Rewrite this to bullet points"');
    
    const rewriteCommand2 = recognizer.detectAICommand("Rewrite this to bullet points");
    if (rewriteCommand2) {
        rewriteCommand2.clipboardContent = mockClipboard.emailContent;
        const response = await recognizer.processWithAI("", rewriteCommand2);
        console.log('   ✅ Detected as REWRITE mode');
        console.log('   📋 Original text:', mockClipboard.emailContent.substring(0, 60) + '...');
        console.log('   🤖 AI Response:', response + '\n');
    }
    
    // Test 5: Reply Mode
    console.log('5. 💬 Testing REPLY MODE:');
    console.log('   Command: "Reply that we don\'t offer these services, in a polite way"');
    
    const replyCommand = recognizer.detectAICommand("Reply that we don't offer these services, in a polite way");
    if (replyCommand) {
        replyCommand.clipboardContent = mockClipboard.emailContent;
        const response = await recognizer.processWithAI("", replyCommand);
        console.log('   ✅ Detected as REPLY mode');
        console.log('   📋 Original message:', mockClipboard.emailContent.substring(0, 60) + '...');
        console.log('   🤖 AI Response:', response + '\n');
    }
    
    // Test 6: Alternative Answer Format
    console.log('6. ❓ Testing ANSWER MODE (alternative format):');
    console.log('   Command: "Answer: What are the benefits of renewable energy?"');
    
    const answerCommand2 = recognizer.detectAICommand("Answer: What are the benefits of renewable energy?");
    if (answerCommand2) {
        const response = await recognizer.processWithAI("", answerCommand2);
        console.log('   ✅ Detected as ANSWER mode');
        console.log('   🤖 AI Response:', response + '\n');
    }
    
    // Test 7: Non-AI Command (Regular Transcription)
    console.log('7. 🎤 Testing REGULAR TRANSCRIPTION (no AI command):');
    console.log('   Text: "This is just regular speech that should be transcribed normally"');
    
    const regularText = recognizer.detectAICommand("This is just regular speech that should be transcribed normally");
    if (!regularText) {
        console.log('   ✅ Correctly identified as regular transcription (no AI processing)');
        console.log('   📝 Would insert: "This is just regular speech that should be transcribed normally"\n');
    }
    
    console.log('🎉 AI Features Test Complete!\n');
    
    console.log('📋 Summary of Supported Commands:');
    console.log('   • Write Mode: "Write [instruction] in my clipboard" or "Write [any request]"');
    console.log('   • Answer Mode: "Answer the question: [question]" or "Answer: [question]"');
    console.log('   • Rewrite Mode: "Rewrite this to [format/language]" (uses clipboard/selected text)');
    console.log('   • Reply Mode: "Reply [instruction]" (uses clipboard for context)');
    console.log('   • Regular Speech: Any text not starting with AI commands gets transcribed directly');
    
    console.log('\n🚀 Ready for real-world testing!');
    console.log('   1. Start the app: npm run dev');
    console.log('   2. Copy some text to clipboard');
    console.log('   3. Press Ctrl+Shift+Space and say an AI command');
    console.log('   4. Watch the magic happen! ✨');
}

// Run the tests
testAIFeatures().catch(console.error);
