# Text Selection Feature for Rewrite and Reply Modes

## Overview

The WhisperTyping Clone now supports automatic text selection for rewrite and reply modes. Instead of requiring users to manually copy text to the clipboard, the application automatically copies selected text when these commands are detected.

## How It Works

### Before (Old Behavior)
1. User manually copies text to clipboard (Ctrl+C/Cmd+C)
2. User presses hotkey and says "rewrite this to be more formal"
3. <PERSON><PERSON> processes clipboard content
4. App inserts result

### After (New Behavior)
1. User selects text in any application
2. User presses hotkey and says "rewrite this to be more formal"
3. App automatically copies selected text while maintaining selection
4. App processes the copied text with AI
5. App **REPLACES** the selected text with the AI result (not insert above)
6. App restores original clipboard content

## Supported Commands

### Rewrite Mode
- **Trigger**: "rewrite [instruction]"
- **Examples**:
  - "rewrite this to be more formal"
  - "rewrite this in Portuguese"
  - "rewrite this as bullet points"
- **Behavior**: Automatically copies selected text and rewrites according to instruction

### Reply Mode
- **Trigger**: "reply [instruction]"
- **Examples**:
  - "reply politely"
  - "reply with a professional tone"
  - "reply declining the offer"
- **Behavior**: Automatically copies selected message/email and generates appropriate reply

## Technical Implementation

### Key Components

#### 1. TextInserter Class Extensions
- `copySelectedText()`: Copies currently selected text
- `copySelectedTextWithPreservation()`: Copies text while preserving clipboard
- `copySelectedTextForReplacement()`: Copies text while maintaining selection for replacement
- `replaceSelectedText()`: Replaces selected text with new content
- `restoreClipboard()`: Restores original clipboard content
- Platform-specific copy and selection simulation methods

#### 2. SpeechRecognizer Enhancements
- `detectAICommandWithTextCopy()`: Enhanced command detection with automatic copying
- Clipboard preservation and restoration logic
- Fallback behavior for empty selections

#### 3. Cross-Platform Support
- **Windows**: PowerShell SendKeys for Ctrl+C
- **macOS**: osascript for Cmd+C
- **Linux**: xdotool for Ctrl+C

### Selection Validation

The system validates that text is actually selected:
1. Clears clipboard
2. Sends copy command (Ctrl+C/Cmd+C)
3. Checks if clipboard contains new content
4. If no content copied, falls back to normal dictation

### Clipboard Preservation

Original clipboard content is preserved throughout the process:
1. Store original clipboard content
2. Perform copy operation
3. Process selected text
4. Restore original clipboard after processing (with delay)

## Usage Examples

### Example 1: Rewriting Selected Text
```
1. Select text: "hey can u help me with this project?"
2. Press hotkey, say: "rewrite this to be more professional"
3. Selected text is REPLACED with: "Could you please assist me with this project?"
```

### Example 2: Replying to Email
```
1. Select email: "We need the report by Friday. Can you deliver?"
2. Press hotkey, say: "reply confirming delivery"
3. Selected text is REPLACED with: "Yes, I can deliver the report by Friday. I'll ensure it meets all requirements."
```

### Example 3: Fallback Behavior
```
1. No text selected
2. Press hotkey, say: "rewrite this to be formal"
3. Result: Normal dictation - "rewrite this to be formal" is typed
```

## Error Handling

### No Text Selected
- App detects empty clipboard after copy attempt
- Falls back to normal dictation mode
- Original clipboard is restored

### Copy Operation Fails
- Falls back to using existing clipboard content
- Logs error for debugging
- Continues with processing

### Platform Not Supported
- Graceful degradation to clipboard-based workflow
- Clear error messages and installation instructions
- Maintains core functionality

## Configuration

### Timing Settings
- Copy operation delay: 200ms
- Clipboard restoration delay: 1500ms
- These can be adjusted based on system performance

### Platform Requirements
- **Windows**: PowerShell (built-in)
- **macOS**: osascript (built-in)
- **Linux**: xdotool or xclip (requires installation)

## Testing

Run the test script to validate functionality:
```bash
npm run build
node test-text-selection.js
```

The test script validates:
- Basic text copying functionality
- Clipboard preservation
- AI command detection with automatic copying
- Fallback behavior for empty selections
- Non-selection commands remain unaffected

## Benefits

### User Experience
- **Seamless workflow**: No manual copying required
- **Intuitive**: Works with natural text selection
- **Safe**: Preserves original clipboard content
- **Reliable**: Fallback to normal dictation if no text selected

### Technical Benefits
- **Cross-platform**: Works on Windows, macOS, and Linux
- **Robust**: Comprehensive error handling
- **Maintainable**: Clean separation of concerns
- **Extensible**: Easy to add new selection-based commands

## Future Enhancements

### Potential Improvements
1. **Visual feedback**: Show when text is being copied
2. **Selection highlighting**: Briefly highlight copied text
3. **Multiple selection support**: Handle multiple text selections
4. **Custom timing**: User-configurable delays
5. **Selection validation**: More sophisticated text detection

### Additional Commands
- **Translate mode**: "translate this to Spanish"
- **Summarize mode**: "summarize this text"
- **Format mode**: "format this as markdown"

## Troubleshooting

### Linux Setup
If text copying doesn't work on Linux:
```bash
# Install xdotool (recommended)
sudo apt-get install xdotool  # Ubuntu/Debian
sudo dnf install xdotool      # Fedora/RHEL
sudo pacman -S xdotool        # Arch

# Or install xclip (alternative)
sudo apt-get install xclip    # Ubuntu/Debian
```

### macOS Permissions
If copy operations fail on macOS:
1. Go to System Preferences > Security & Privacy > Accessibility
2. Add WhisperTyping Clone to allowed applications
3. Restart the application

### Windows Issues
If PowerShell commands fail:
1. Check PowerShell execution policy
2. Run as administrator if needed
3. Ensure Windows is up to date

## API Reference

### TextInserter Methods
```typescript
// Copy selected text and return it
async copySelectedText(): Promise<string | null>

// Copy with clipboard preservation
async copySelectedTextWithPreservation(): Promise<{
  selectedText: string | null;
  originalClipboard: string;
}>

// Copy selected text while maintaining selection for replacement
async copySelectedTextForReplacement(): Promise<{
  selectedText: string | null;
  originalClipboard: string;
}>

// Replace selected text with new content
async replaceSelectedText(
  newText: string,
  originalClipboard: string,
  originalText: string
): Promise<boolean>

// Restore clipboard content
restoreClipboard(originalContent: string): void
```

### SpeechRecognizer Methods
```typescript
// Enhanced command detection with automatic copying
async detectAICommandWithTextCopy(text: string): Promise<AIProcessingOptions | null>
```
