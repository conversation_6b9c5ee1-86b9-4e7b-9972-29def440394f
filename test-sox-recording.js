#!/usr/bin/env node

/**
 * Test script to verify the fixed SoX recording pipeline
 * 
 * This script will:
 * 1. Test short recordings (2-3 seconds)
 * 2. Test medium recordings (10-15 seconds) 
 * 3. Test longer recordings (30+ seconds)
 * 4. Verify WAV format conversion
 * 5. Check for SoX process stability
 */

const { AudioRecorder } = require('./dist/audio/recorder');
const fs = require('fs');
const path = require('path');

class RecordingTester {
  constructor() {
    this.testResults = [];
    this.testOutputDir = './test-recordings';
    
    // Ensure output directory exists
    if (!fs.existsSync(this.testOutputDir)) {
      fs.mkdirSync(this.testOutputDir);
    }
  }

  async runAllTests() {
    console.log('🎯 Starting SoX Recording Pipeline Tests');
    console.log('=====================================\n');

    const tests = [
      { name: 'Short Recording', duration: 2000 },
      { name: 'Medium Recording', duration: 10000 },
      { name: 'Long Recording', duration: 30000 },
      { name: 'Very Long Recording', duration: 45000 }
    ];

    for (const test of tests) {
      await this.runRecordingTest(test.name, test.duration);
      // Wait between tests to avoid conflicts
      await this.sleep(2000);
    }

    this.printSummary();
  }

  async runRecordingTest(testName, duration) {
    console.log(`\n📊 Running Test: ${testName} (${duration}ms)`);
    console.log('─'.repeat(50));

    const recorder = new AudioRecorder();
    const testResult = {
      name: testName,
      duration: duration,
      success: false,
      error: null,
      audioSize: 0,
      wavSize: 0,
      soxExitCode: null
    };

    try {
      // Setup error monitoring
      recorder.on('error', (error) => {
        console.error(`❌ Recording error: ${error.message}`);
        testResult.error = error.message;
      });

      // Start recording
      console.log('🎤 Starting recording...');
      await recorder.start();
      
      // Record for specified duration
      console.log(`⏱️  Recording for ${duration}ms...`);
      await this.sleep(duration);
      
      // Stop and get WAV data
      console.log('🛑 Stopping recording and converting to WAV...');
      const wavData = await recorder.stopAndGetWAV();
      
      if (wavData) {
        testResult.wavSize = wavData.length;
        testResult.success = true;
        
        // Save the WAV file for inspection
        const filename = `${testName.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}.wav`;
        const filepath = path.join(this.testOutputDir, filename);
        fs.writeFileSync(filepath, wavData);
        
        console.log(`✅ Test passed!`);
        console.log(`   WAV size: ${wavData.length} bytes`);
        console.log(`   Saved to: ${filepath}`);
        
        // Verify WAV format
        if (this.isValidWAV(wavData)) {
          console.log(`   ✓ Valid WAV format confirmed`);
        } else {
          console.log(`   ⚠️  WAV format validation failed`);
          testResult.success = false;
          testResult.error = 'Invalid WAV format';
        }
        
      } else {
        testResult.success = false;
        testResult.error = 'No audio data returned';
        console.log(`❌ Test failed: No audio data returned`);
      }
      
    } catch (error) {
      testResult.success = false;
      testResult.error = error.message;
      console.error(`❌ Test failed with exception: ${error.message}`);
    } finally {
      // Cleanup
      await recorder.cleanup();
    }

    this.testResults.push(testResult);
  }

  isValidWAV(buffer) {
    if (buffer.length < 44) return false;
    
    const riffHeader = buffer.slice(0, 4).toString('ascii');
    const waveFormat = buffer.slice(8, 12).toString('ascii');
    
    return riffHeader === 'RIFF' && waveFormat === 'WAVE';
  }

  printSummary() {
    console.log('\n🏁 Test Summary');
    console.log('=====================================');
    
    const passed = this.testResults.filter(r => r.success).length;
    const total = this.testResults.length;
    
    console.log(`Overall: ${passed}/${total} tests passed\n`);
    
    this.testResults.forEach(result => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      console.log(`${status} ${result.name}`);
      if (result.success) {
        console.log(`     Duration: ${result.duration}ms, WAV Size: ${result.wavSize} bytes`);
      } else {
        console.log(`     Error: ${result.error}`);
      }
    });

    if (passed === total) {
      console.log('\n🎉 All tests passed! SoX recording pipeline is working correctly.');
    } else {
      console.log('\n⚠️  Some tests failed. Check the errors above for details.');
    }

    console.log(`\nTest recordings saved to: ${this.testOutputDir}`);
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the tests
async function main() {
  try {
    const tester = new RecordingTester();
    await tester.runAllTests();
  } catch (error) {
    console.error('Test runner failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { RecordingTester };
