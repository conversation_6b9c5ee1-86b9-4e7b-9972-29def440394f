import axios from 'axios';
import FormData from 'form-data';
import { clipboard } from 'electron';

export interface TranscriptionOptions {
  language?: string;
  prompt?: string;
  temperature?: number;
  response_format?: 'json' | 'text' | 'srt' | 'verbose_json' | 'vtt';
}

export interface AIProcessingOptions {
  mode: 'write' | 'answer' | 'rewrite' | 'reply' | 'command';
  prompt: string;
  context?: string;
  selectedText?: string;
  clipboardContent?: string;
}

export class SpeechRecognizer {
  private openaiApiKey: string = '';
  private whisperApiUrl = 'https://api.openai.com/v1/audio/transcriptions';
  private gptApiUrl = 'https://api.openai.com/v1/chat/completions';

  constructor() {
    // Load API key from config or environment
    this.openaiApiKey = process.env.OPENAI_API_KEY || '';
  }

  setApiKey(apiKey: string): void {
    this.openaiApiKey = apiKey;
  }

  async testApiKey(apiKey?: string): Promise<boolean> {
    const keyToTest = apiKey || this.openaiApiKey;
    
    if (!keyToTest) {
      return false;
    }

    try {
      const response = await axios.get('https://api.openai.com/v1/models', {
        headers: {
          'Authorization': `Bearer ${keyToTest}`,
        },
        timeout: 10000
      });
      
      return response.status === 200;
    } catch (error) {
      console.error('API key test failed:', error);
      return false;
    }
  }

  async transcribe(audioData: Buffer, options: TranscriptionOptions = {}): Promise<string | null> {
    // For testing purposes, return a mock transcription if no API key is configured
    if (!this.openaiApiKey) {
      console.log('No API key configured, returning mock transcription');
      return `Mock transcription of ${audioData.length} bytes of audio data. This would be the actual transcribed text from Whisper API.`;
    }

    try {
      console.log(`Transcribing ${audioData.length} bytes of audio data...`);

      // Validate that we have proper WAV format
      if (!this.isValidWAVBuffer(audioData)) {
        throw new Error('Invalid audio format: Expected WAV format with proper header');
      }

      // Create form data for the API request
      const formData = new FormData();
      
      // Add the audio file
      formData.append('file', audioData, {
        filename: 'speech.wav',
        contentType: 'audio/wav'
      });
      
      // Add model
      formData.append('model', 'whisper-1');
      
      // Add optional parameters
      if (options.language) {
        formData.append('language', options.language);
      }
      
      if (options.prompt) {
        formData.append('prompt', options.prompt);
      }
      
      if (options.temperature !== undefined) {
        formData.append('temperature', options.temperature.toString());
      }
      
      formData.append('response_format', options.response_format || 'text');

      const response = await axios.post(this.whisperApiUrl, formData, {
        headers: {
          'Authorization': `Bearer ${this.openaiApiKey}`,
          ...formData.getHeaders()
        },
        timeout: 30000 // 30 second timeout
      });

      if (response.data) {
        // Handle different response formats
        if (options.response_format === 'json' || options.response_format === 'verbose_json') {
          return response.data.text || '';
        } else {
          return response.data || '';
        }
      }

      return null;
    } catch (error: any) {
      console.error('Transcription failed:', error);

      if (error.response) {
        console.error('API Error:', error.response.data);
        throw new Error(`Transcription failed: ${error.response.data.error?.message || 'Unknown API error'}`);
      } else if (error.request) {
        throw new Error('Network error: Unable to reach OpenAI API');
      } else {
        throw new Error(`Transcription failed: ${error.message || String(error)}`);
      }
    }
  }

  async processWithAI(text: string, options: AIProcessingOptions): Promise<string | null> {
    if (!this.openaiApiKey) {
      // Return mock AI responses for demo mode
      console.log('No API key configured, returning mock AI response');
      return `[DEMO MODE] Mock ${options.mode} response for: "${options.prompt}". This would be generated by GPT-4 when an API key is configured.`;
    }

    try {
      let systemPrompt = '';
      let userPrompt = '';

      // Get clipboard content for context
      const clipboardContent = options.clipboardContent || this.safeReadClipboard();

      // Configure prompts based on AI mode
      switch (options.mode) {
        case 'write':
          systemPrompt = 'You are a helpful writing assistant. Generate clear, well-written text based on the user\'s request. If the user mentions "clipboard", use the provided clipboard content as context.';
          if (clipboardContent && options.prompt.toLowerCase().includes('clipboard')) {
            userPrompt = `Write: ${options.prompt}\n\nClipboard content for context: "${clipboardContent}"`;
          } else {
            userPrompt = `Write: ${options.prompt}`;
          }
          break;

        case 'answer':
          systemPrompt = 'You are a knowledgeable assistant. Provide accurate, helpful answers to questions. Be concise but thorough.';
          userPrompt = `Answer the question: ${options.prompt}`;
          break;

        case 'rewrite':
          systemPrompt = 'You are a text editing assistant. Rewrite the provided text according to the user\'s instructions. Maintain the original meaning unless specifically asked to change it.';
          const textToRewrite = options.selectedText || clipboardContent || text;
          userPrompt = `Rewrite the following text according to these instructions: "${options.prompt}"\n\nText to rewrite: "${textToRewrite}"`;
          break;

        case 'reply':
          systemPrompt = 'You are a communication assistant. Help write appropriate replies to messages or emails. Match the tone and formality of the original message.';
          const messageToReplyTo = clipboardContent || options.context || text;
          userPrompt = `Write a reply to the following message/email: "${messageToReplyTo}"\n\nReply instructions: ${options.prompt}`;
          break;

        case 'command':
          systemPrompt = 'You are a command execution assistant. Interpret user commands and provide appropriate responses or actions.';
          userPrompt = `Execute command: ${options.prompt}`;
          break;

        default:
          throw new Error(`Unknown AI mode: ${options.mode}`);
      }

      const response = await axios.post(this.gptApiUrl, {
        model: 'gpt-4.1-nano',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        max_tokens: 1000,
        temperature: 0.7
      }, {
        headers: {
          'Authorization': `Bearer ${this.openaiApiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 30000
      });

      if (response.data?.choices?.[0]?.message?.content) {
        return response.data.choices[0].message.content.trim();
      }

      return null;
    } catch (error: any) {
      console.error('AI processing failed:', error);

      if (error.response) {
        console.error('API Error:', error.response.data);
        throw new Error(`AI processing failed: ${error.response.data.error?.message || 'Unknown API error'}`);
      } else if (error.request) {
        throw new Error('Network error: Unable to reach OpenAI API');
      } else {
        throw new Error(`AI processing failed: ${error.message || String(error)}`);
      }
    }
  }

  /**
   * Validate that the buffer contains a proper WAV file
   */
  private isValidWAVBuffer(buffer: Buffer): boolean {
    if (buffer.length < 44) {
      console.warn('Audio buffer too small for WAV format (< 44 bytes)');
      return false;
    }

    // Check for RIFF header
    const riffHeader = buffer.slice(0, 4).toString('ascii');
    if (riffHeader !== 'RIFF') {
      console.warn(`Invalid WAV header: Expected 'RIFF', got '${riffHeader}'`);
      return false;
    }

    // Check for WAVE format
    const waveFormat = buffer.slice(8, 12).toString('ascii');
    if (waveFormat !== 'WAVE') {
      console.warn(`Invalid WAV format: Expected 'WAVE', got '${waveFormat}'`);
      return false;
    }

    console.log('✓ Valid WAV format detected');
    return true;
  }

  // Helper method to safely read clipboard
  private safeReadClipboard(): string {
    try {
      return clipboard?.readText() || '';
    } catch (error: any) {
      console.warn('Clipboard not available:', error?.message || 'Unknown error');
      return '';
    }
  }

  // Method to detect if the transcribed text is an AI command
  detectAICommand(text: string): AIProcessingOptions | null {
    const lowerText = text.toLowerCase().trim();

    // Write mode - supports clipboard references
    if (lowerText.startsWith('write ')) {
      const prompt = text.substring(6).trim();
      return {
        mode: 'write',
        prompt: prompt,
        clipboardContent: prompt.toLowerCase().includes('clipboard') ? this.safeReadClipboard() : undefined
      };
    }

    // Answer mode - direct questions to GPT-4
    if (lowerText.startsWith('answer ')) {
      return {
        mode: 'answer',
        prompt: text.substring(7).trim()
      };
    }

    // Alternative answer patterns
    if (lowerText.startsWith('answer the question:') || lowerText.startsWith('answer:')) {
      const colonIndex = text.indexOf(':');
      return {
        mode: 'answer',
        prompt: text.substring(colonIndex + 1).trim()
      };
    }

    // Rewrite mode - works with selected text or clipboard
    if (lowerText.startsWith('rewrite ')) {
      return {
        mode: 'rewrite',
        prompt: text.substring(8).trim(),
        clipboardContent: this.safeReadClipboard()
      };
    }

    // Reply mode - uses clipboard for email/message context
    if (lowerText.startsWith('reply ')) {
      return {
        mode: 'reply',
        prompt: text.substring(6).trim(),
        clipboardContent: this.safeReadClipboard()
      };
    }

    // Command mode
    if (lowerText.startsWith('run command ') || lowerText.startsWith('open ')) {
      return {
        mode: 'command',
        prompt: text.trim()
      };
    }

    return null;
  }

  // Get supported languages for Whisper
  getSupportedLanguages(): string[] {
    return [
      'en', 'zh', 'de', 'es', 'ru', 'ko', 'fr', 'ja', 'pt', 'tr', 'pl', 'ca', 'nl', 
      'ar', 'sv', 'it', 'id', 'hi', 'fi', 'vi', 'he', 'uk', 'el', 'ms', 'cs', 'ro', 
      'da', 'hu', 'ta', 'no', 'th', 'ur', 'hr', 'bg', 'lt', 'la', 'mi', 'ml', 'cy', 
      'sk', 'te', 'fa', 'lv', 'bn', 'sr', 'az', 'sl', 'kn', 'et', 'mk', 'br', 'eu', 
      'is', 'hy', 'ne', 'mn', 'bs', 'kk', 'sq', 'sw', 'gl', 'mr', 'pa', 'si', 'km', 
      'sn', 'yo', 'so', 'af', 'oc', 'ka', 'be', 'tg', 'sd', 'gu', 'am', 'yi', 'lo', 
      'uz', 'fo', 'ht', 'ps', 'tk', 'nn', 'mt', 'sa', 'lb', 'my', 'bo', 'tl', 'mg', 
      'as', 'tt', 'haw', 'ln', 'ha', 'ba', 'jw', 'su'
    ];
  }
}
