import { app, BrowserWindow, ipcMain, Tray, Menu, globalShortcut, dialog } from 'electron';

// Add a custom property to track app quitting state
let isQuiting = false;
import * as path from 'path';
import { AudioRecorder } from './audio/recorder';
import { SpeechRecognizer } from './speech/recognizer';
import { TextInserter } from './text/inserter';
import { ConfigManager } from './config/manager';
import { HotkeyManager } from './hotkey/manager';

class WhisperTypingApp {
  private mainWindow: BrowserWindow | null = null;
  private tray: Tray | null = null;
  private audioRecorder: AudioRecorder;
  private speechRecognizer: SpeechRecognizer;
  private textInserter: TextInserter;
  private configManager: ConfigManager;
  private hotkeyManager: HotkeyManager;
  private isRecording = false;
  private recordingTimeout: NodeJS.Timeout | null = null;
  private maxRecordingDuration = 60000; // 60 seconds max recording
  private stateCheckInterval: NodeJS.Timeout | null = null;
  private lastKeyUpTime = 0;
  private recordingStartTime = 0;
  private keyCurrentlyPressed = false;

  constructor() {
    this.audioRecorder = new AudioRecorder();
    this.audioRecorder.on('error', (error) => {
      console.error('Audio recording error:', error);
      dialog.showErrorBox('Recording Error', error.message || 'An error occurred during audio recording.');
      this.isRecording = false;
      this.updateTrayIcon(false);
    });

    this.audioRecorder.on('recording-too-short', (data) => {
      console.warn(`Recording too short: ${data.duration}ms (minimum: ${data.minimum}ms)`);
      // Don't show error for very short recordings, just ignore them
    });

    this.audioRecorder.on('no-audio-captured', () => {
      console.warn('No audio data captured during recording');
      // Don't show error, this can happen with very quick key presses
    });
    this.speechRecognizer = new SpeechRecognizer();
    this.textInserter = new TextInserter();
    this.configManager = new ConfigManager();
    this.hotkeyManager = new HotkeyManager();

    // Set API key from config
    const apiKey = this.configManager.getApiKey();
    if (apiKey) {
      this.speechRecognizer.setApiKey(apiKey);
    }
  }

  async initialize() {
    // Setup IPC handlers first, before app is ready
    this.setupIpcHandlers();

    await app.whenReady();

    this.createWindow();
    this.createTray();
    this.registerGlobalHotkey();

    // Hide window on startup (run in background)
    if (this.mainWindow) {
      this.mainWindow.hide();
    }
  }

  private createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 400,
      height: 600,
      show: false,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      },
      icon: path.join(__dirname, '../assets/icon.png'),
      title: 'WhisperTyping Clone'
    });

    // Load the HTML file
    this.mainWindow.loadFile(path.join(__dirname, '../assets/index.html'));

    // Handle window closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Hide window instead of closing when user clicks X
    this.mainWindow.on('close', (event) => {
      if (!isQuiting) {
        event.preventDefault();
        this.mainWindow?.hide();
      }
    });
  }

  private createTray() {
    try {
      const iconPath = path.join(__dirname, '../assets/tray-icon.png');
      this.tray = new Tray(iconPath);
    
    const contextMenu = Menu.buildFromTemplate([
      {
        label: 'Show Window',
        click: () => {
          this.mainWindow?.show();
        }
      },
      {
        label: this.isRecording ? 'Stop Recording' : 'Start Recording',
        click: () => {
          if (this.isRecording) {
            this.stopRecording();
          } else {
            this.startRecording();
          }
        }
      },
      { type: 'separator' },
      {
        label: 'Settings',
        click: () => {
          this.mainWindow?.show();
        }
      },
      { type: 'separator' },
      {
        label: 'Quit',
        click: () => {
          isQuiting = true;
          app.quit();
        }
      }
    ]);

    this.tray.setContextMenu(contextMenu);
    this.tray.setToolTip('WhisperTyping Clone');
    
      // Show window on tray click
      this.tray.on('click', () => {
        this.mainWindow?.show();
      });
    } catch (error) {
      console.error('Failed to create tray icon:', error);
      // Continue without tray icon if creation fails
    }
  }

  private async registerGlobalHotkey() {
    try {
      // Get hotkey from config
      const configuredHotkey = this.configManager.getHotkey();

      const result = await this.hotkeyManager.registerPressHoldHotkey({
        accelerator: configuredHotkey,
        onKeyDown: () => {
          console.log(`${configuredHotkey} pressed down - starting recording`);
          this.keyCurrentlyPressed = true;
          this.startRecording();
        },
        onKeyUp: () => {
          console.log(`${configuredHotkey} released - stopping recording`);
          this.keyCurrentlyPressed = false;
          this.lastKeyUpTime = Date.now();
          this.stopRecording();
        },
        description: 'Press and hold for speech recording'
      });

      if (result.success) {
        console.log(`✅ Global press-hold hotkey registered: ${configuredHotkey}`);
        console.log(`Press and hold ${HotkeyManager.formatAcceleratorForDisplay(configuredHotkey)} to record`);
      } else {
        console.warn(`❌ Failed to register press-hold hotkey: ${result.error}`);
        // Try fallback hotkeys
        await this.tryFallbackHotkeys();
      }
    } catch (error) {
      console.error('Failed to register hotkey:', error);
      await this.tryFallbackHotkeys();
    }
  }

  private async tryFallbackHotkeys() {
    const fallbackHotkeys = HotkeyManager.getDefaultHotkeySuggestions();
    let registeredFallback = false;

    for (const hotkey of fallbackHotkeys) {
      try {
        const result = await this.hotkeyManager.registerPressHoldHotkey({
          accelerator: hotkey,
          onKeyDown: () => {
            console.log(`${hotkey} pressed down - starting recording`);
            this.keyCurrentlyPressed = true;
            this.startRecording();
          },
          onKeyUp: () => {
            console.log(`${hotkey} released - stopping recording`);
            this.keyCurrentlyPressed = false;
            this.lastKeyUpTime = Date.now();
            this.stopRecording();
          },
          description: 'Press and hold for speech recording (fallback)'
        });

        if (result.success) {
          console.log(`✅ Fallback press-hold hotkey registered: ${hotkey}`);
          // Update config with working hotkey
          this.configManager.setHotkey(hotkey);
          registeredFallback = true;

          // Notify user about fallback
          this.mainWindow?.webContents.send('hotkey-fallback-used', {
            originalHotkey: this.configManager.getHotkey(),
            fallbackHotkey: hotkey,
            message: `Your configured hotkey was not available. Using ${HotkeyManager.formatAcceleratorForDisplay(hotkey)} for press-and-hold recording instead.`
          });
          break;
        }
      } catch (error) {
        console.warn(`Failed to register fallback hotkey ${hotkey}:`, error);
      }
    }

    if (!registeredFallback) {
      console.error('❌ Failed to register any hotkey, including fallbacks');
      this.mainWindow?.webContents.send('hotkey-registration-failed', {
        message: 'Unable to register any global hotkey. You can still use the app manually.'
      });
    }
  }




  private setupIpcHandlers() {
    ipcMain.handle('start-recording', async () => {
      return await this.startRecording();
    });

    ipcMain.handle('stop-recording', async () => {
      return await this.stopRecording();
    });

    ipcMain.handle('get-config', () => {
      return this.configManager.getConfig();
    });

    ipcMain.handle('update-config', (event, config) => {
      const success = this.configManager.updateConfig(config);
      if (success && config.openaiApiKey) {
        // Update the speech recognizer with the new API key
        this.speechRecognizer.setApiKey(config.openaiApiKey);
      }
      return success;
    });

    ipcMain.handle('test-api-key', async (event, apiKey) => {
      return await this.speechRecognizer.testApiKey(apiKey);
    });

    ipcMain.handle('update-hotkey', async (event, newHotkey) => {
      return await this.updateHotkey(newHotkey);
    });

    ipcMain.handle('test-hotkey', async (event, hotkey) => {
      return await this.hotkeyManager.testHotkey(hotkey);
    });

    ipcMain.handle('get-supported-keys', () => {
      return HotkeyManager.getSupportedKeys();
    });

    ipcMain.handle('validate-hotkey', (event, hotkey) => {
      return HotkeyManager.validateAccelerator(hotkey);
    });

    ipcMain.handle('format-hotkey', (event, hotkey) => {
      return HotkeyManager.formatAcceleratorForDisplay(hotkey);
    });

    ipcMain.handle('get-current-hotkey', () => {
      return this.hotkeyManager.getCurrentHotkey();
    });

    ipcMain.handle('reset-hotkey-to-default', async () => {
      const defaultHotkey = 'CommandOrControl+Shift+Space';
      const result = await this.updateHotkey(defaultHotkey);
      return result;
    });
  }

  private async startRecording(): Promise<boolean> {
    try {
      if (this.isRecording) {
        console.log('Recording already in progress, ignoring start request');
        return false;
      }

      console.log('Starting recording...');
      this.isRecording = true;
      this.recordingStartTime = Date.now();

      // Update tray icon to show recording state
      this.updateTrayIcon(true);

      // Start audio recording
      await this.audioRecorder.start();

      // Set up safety timeout to prevent stuck recordings
      this.recordingTimeout = setTimeout(() => {
        console.warn(`⚠️ Recording timeout reached (${this.maxRecordingDuration}ms), force stopping...`);
        this.forceStopRecording();
      }, this.maxRecordingDuration);

      // Start state monitoring
      this.startStateMonitoring();

      // Notify renderer process
      this.mainWindow?.webContents.send('recording-started');

      return true;
    } catch (error) {
      console.error('Failed to start recording:', error);
      this.isRecording = false;
      this.updateTrayIcon(false);

      // Show user-friendly error message
      if (error instanceof Error) {
        dialog.showErrorBox('Recording Error', error.message);
      }

      return false;
    }
  }

  private async stopRecording(): Promise<boolean> {
    try {
      if (!this.isRecording) {
        console.log('No recording in progress, ignoring stop request');
        return false;
      }
      
      console.log('Stopping recording...');
      this.isRecording = false;

      // Stop state monitoring
      this.stopStateMonitoring();

      // Clear the safety timeout
      if (this.recordingTimeout) {
        clearTimeout(this.recordingTimeout);
        this.recordingTimeout = null;
      }

      // Update tray icon
      this.updateTrayIcon(false);
      
      // Stop audio recording and get the audio data in WAV format
      const audioData = await this.audioRecorder.stopAndGetWAV();

      if (audioData) {
        // Send audio to speech recognition
        const transcription = await this.speechRecognizer.transcribe(audioData);

        if (transcription) {
          // Check if this is an AI command with automatic text copying for rewrite/reply modes
          const aiCommand = await this.speechRecognizer.detectAICommandWithTextCopy(transcription);

          if (aiCommand) {
            console.log(`Detected AI command: ${aiCommand.mode} - ${aiCommand.prompt}`);

            // Process with AI
            const aiResponse = await this.speechRecognizer.processWithAI(transcription, aiCommand);

            if (aiResponse) {
              // Insert the AI-generated response
              await this.textInserter.insertText(aiResponse);

              // Notify renderer process with AI response
              this.mainWindow?.webContents.send('ai-response-complete', {
                mode: aiCommand.mode,
                prompt: aiCommand.prompt,
                response: aiResponse,
                originalTranscription: transcription
              });
            }
          } else {
            // Regular transcription - insert the transcribed text directly
            await this.textInserter.insertText(transcription);

            // Notify renderer process
            this.mainWindow?.webContents.send('transcription-complete', transcription);
          }
        }
      } else {
        console.log('No audio data to process (recording was too short or no audio captured)');
      }
      
      // Notify renderer process
      this.mainWindow?.webContents.send('recording-stopped');
      
      return true;
    } catch (error) {
      console.error('Failed to stop recording:', error);
      this.isRecording = false;
      this.updateTrayIcon(false);

      // Show user-friendly error message
      if (error instanceof Error) {
        dialog.showErrorBox('Processing Error', error.message);
      }

      return false;
    }
  }

  /**
   * Start monitoring recording state for recovery
   */
  private startStateMonitoring(): void {
    if (this.stateCheckInterval) {
      clearInterval(this.stateCheckInterval);
    }

    this.stateCheckInterval = setInterval(() => {
      this.checkRecordingState();
    }, 2000); // Check every 2 seconds
  }

  /**
   * Stop state monitoring
   */
  private stopStateMonitoring(): void {
    if (this.stateCheckInterval) {
      clearInterval(this.stateCheckInterval);
      this.stateCheckInterval = null;
    }
  }

  /**
   * Check for stuck recording states and recover
   */
  private checkRecordingState(): void {
    if (!this.isRecording) {
      this.stopStateMonitoring();
      return;
    }

    const currentTime = Date.now();
    const recordingDuration = currentTime - this.recordingStartTime;

    // Only check for stuck state if we have a valid lastKeyUpTime and the key is not currently pressed
    if (this.lastKeyUpTime > 0 && !this.keyCurrentlyPressed) {
      const timeSinceLastKeyUp = currentTime - this.lastKeyUpTime;
      const maxStuckTime = 10000; // Increased to 10 seconds to reduce false positives

      if (timeSinceLastKeyUp > maxStuckTime) {
        console.warn(`⚠️ Recording appears stuck (${timeSinceLastKeyUp}ms since last keyup, key not pressed), force stopping...`);
        this.forceStopRecording();
        return;
      }
    }

    // Check if recording has been going for an extremely long time (safety check)
    const maxRecordingTime = 120000; // 2 minutes absolute maximum
    if (recordingDuration > maxRecordingTime) {
      console.warn(`⚠️ Recording exceeded maximum duration (${recordingDuration}ms), force stopping...`);
      this.forceStopRecording();
      return;
    }

    // Additional check: verify audio recorder state matches our state
    if (this.isRecording && !this.audioRecorder.isCurrentlyRecording()) {
      console.warn('⚠️ Recording state mismatch detected, force stopping...');
      this.forceStopRecording();
    }
  }

  /**
   * Force stop recording (used by timeout and cleanup)
   */
  private forceStopRecording(): void {
    if (!this.isRecording) return;

    console.log('Force stopping recording...');
    this.isRecording = false;
    this.keyCurrentlyPressed = false; // Reset key state
    this.lastKeyUpTime = Date.now(); // Update last keyup time

    // Stop state monitoring
    this.stopStateMonitoring();

    // Clear the safety timeout
    if (this.recordingTimeout) {
      clearTimeout(this.recordingTimeout);
      this.recordingTimeout = null;
    }

    // Update tray icon
    this.updateTrayIcon(false);

    // Force stop audio recording
    this.audioRecorder.forceStop();

    // Notify renderer process
    this.mainWindow?.webContents.send('recording-stopped');
    this.mainWindow?.webContents.send('recording-force-stopped', {
      reason: 'stuck_detection',
      message: 'Recording was automatically stopped due to stuck key detection or system issue'
    });

    // Clear any "Processing..." state in the UI
    this.mainWindow?.webContents.send('processing-stopped');
  }

  private updateTrayIcon(recording: boolean) {
    const iconName = recording ? 'tray-icon-recording.png' : 'tray-icon.png';
    const iconPath = path.join(__dirname, '../assets', iconName);
    this.tray?.setImage(iconPath);
  }

  private async updateHotkey(newHotkey: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Validate the new hotkey
      const validation = HotkeyManager.validateAccelerator(newHotkey);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      // Test if the hotkey is available
      const availability = await this.hotkeyManager.testHotkey(newHotkey);
      if (!availability.available) {
        return { success: false, error: availability.error };
      }

      // Register the new press-hold hotkey
      const result = await this.hotkeyManager.registerPressHoldHotkey({
        accelerator: newHotkey,
        onKeyDown: () => {
          console.log(`${newHotkey} pressed down - starting recording`);
          this.keyCurrentlyPressed = true;
          this.startRecording();
        },
        onKeyUp: () => {
          console.log(`${newHotkey} released - stopping recording`);
          this.keyCurrentlyPressed = false;
          this.lastKeyUpTime = Date.now();
          this.stopRecording();
        },
        description: 'Press and hold for speech recording'
      });

      if (result.success) {
        // Update config with new hotkey
        this.configManager.setHotkey(newHotkey);
        console.log(`✅ Hotkey updated to: ${newHotkey}`);
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (error) {
      console.error('Failed to update hotkey:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  async cleanup() {
    console.log('Cleaning up WhisperTyping application...');

    // Force stop any ongoing recording
    if (this.isRecording) {
      this.forceStopRecording();
    }

    // Unregister global shortcuts using our hotkey manager
    this.hotkeyManager.cleanup();

    // Clean up resources
    await this.audioRecorder.cleanup();

    console.log('Cleanup completed');
  }
}

// App event handlers
app.on('ready', async () => {
  const whisperApp = new WhisperTypingApp();
  await whisperApp.initialize();
});

app.on('window-all-closed', () => {
  // On macOS, keep the app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // On macOS, re-create window when dock icon is clicked
  if (BrowserWindow.getAllWindows().length === 0) {
    const whisperApp = new WhisperTypingApp();
    whisperApp.initialize();
  }
});

app.on('before-quit', async () => {
  isQuiting = true;
});
