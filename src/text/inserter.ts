import { clipboard } from 'electron';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export class TextInserter {
  private platform: string;

  constructor() {
    this.platform = process.platform;
  }

  async insertText(text: string): Promise<boolean> {
    try {
      console.log(`Inserting text: "${text}"`);
      
      // Store current clipboard content
      const originalClipboard = clipboard.readText();
      
      // Set the text to clipboard
      clipboard.writeText(text);
      
      // Simulate paste operation based on platform
      await this.simulatePaste();
      
      // Restore original clipboard content after a short delay
      setTimeout(() => {
        clipboard.writeText(originalClipboard);
      }, 1000);
      
      return true;
    } catch (error) {
      console.error('Failed to insert text:', error);
      return false;
    }
  }

  private async simulatePaste(): Promise<void> {
    try {
      switch (this.platform) {
        case 'win32':
          await this.simulatePasteWindows();
          break;
        case 'darwin':
          await this.simulatePasteMacOS();
          break;
        case 'linux':
          await this.simulatePasteLinux();
          break;
        default:
          throw new Error(`Unsupported platform: ${this.platform}`);
      }
    } catch (error) {
      console.error('Failed to simulate paste:', error);
      throw error;
    }
  }

  private async simulatePasteWindows(): Promise<void> {
    // On Windows, we can use PowerShell to send Ctrl+V
    const command = `
      Add-Type -AssemblyName System.Windows.Forms
      [System.Windows.Forms.SendKeys]::SendWait("^v")
    `;
    
    await execAsync(`powershell -Command "${command}"`);
  }

  private async simulatePasteMacOS(): Promise<void> {
    // On macOS, we can use osascript to send Cmd+V
    const command = `osascript -e 'tell application "System Events" to keystroke "v" using command down'`;
    await execAsync(command);
  }

  private async simulatePasteLinux(): Promise<void> {
    // On Linux, we can use xdotool to send Ctrl+V
    // First check if xdotool is available
    try {
      await execAsync('which xdotool');
      await execAsync('xdotool key ctrl+v');
    } catch (error) {
      // Fallback to xclip + xdotool alternative
      try {
        await execAsync('which xclip');
        await execAsync('xdotool key ctrl+v');
      } catch (fallbackError) {
        console.warn('Neither xdotool nor xclip found. Text insertion may not work properly.');
        throw new Error('Required tools (xdotool or xclip) not found for text insertion on Linux');
      }
    }
  }

  // Alternative method using robotjs (when available)
  async insertTextWithRobotJS(text: string): Promise<boolean> {
    try {
      // This would require robotjs to be properly installed
      // For now, we'll use the clipboard method as fallback
      console.log('RobotJS not available, using clipboard method');
      return await this.insertText(text);
    } catch (error) {
      console.error('RobotJS text insertion failed:', error);
      return false;
    }
  }

  // Method to type text character by character (slower but more reliable)
  async typeText(text: string, delay: number = 50): Promise<boolean> {
    try {
      for (const char of text) {
        await this.typeCharacter(char);
        if (delay > 0) {
          await this.sleep(delay);
        }
      }
      return true;
    } catch (error) {
      console.error('Failed to type text:', error);
      return false;
    }
  }

  private async typeCharacter(char: string): Promise<void> {
    // Store and restore clipboard for each character
    const originalClipboard = clipboard.readText();
    clipboard.writeText(char);
    await this.simulatePaste();
    clipboard.writeText(originalClipboard);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Method to copy currently selected text to clipboard
  async copySelectedText(): Promise<string | null> {
    try {
      console.log('Attempting to copy selected text...');

      // Store original clipboard content
      const originalClipboard = clipboard.readText();

      // Clear clipboard to detect if copy operation succeeds
      clipboard.writeText('');

      // Wait a moment for clipboard to clear
      await this.sleep(50);

      // Simulate copy operation based on platform
      await this.simulateCopy();

      // Wait for copy operation to complete
      await this.sleep(200);

      // Read the clipboard to see if text was copied
      const copiedText = clipboard.readText();

      // Restore original clipboard content
      clipboard.writeText(originalClipboard);

      // Check if we actually copied something
      if (copiedText && copiedText.trim().length > 0) {
        console.log(`Successfully copied selected text: "${copiedText.substring(0, 50)}${copiedText.length > 50 ? '...' : ''}"`);
        return copiedText;
      } else {
        console.log('No text was selected or copy operation failed');
        return null;
      }
    } catch (error) {
      console.error('Failed to copy selected text:', error);
      return null;
    }
  }

  // Method to copy selected text with clipboard preservation
  async copySelectedTextWithPreservation(): Promise<{ selectedText: string | null; originalClipboard: string }> {
    try {
      console.log('Copying selected text with clipboard preservation...');

      // Store original clipboard content
      const originalClipboard = clipboard.readText();

      // Clear clipboard to detect if copy operation succeeds
      clipboard.writeText('');

      // Wait a moment for clipboard to clear
      await this.sleep(50);

      // Simulate copy operation based on platform
      await this.simulateCopy();

      // Wait for copy operation to complete
      await this.sleep(200);

      // Read the clipboard to see if text was copied
      const copiedText = clipboard.readText();

      // Check if we actually copied something
      if (copiedText && copiedText.trim().length > 0) {
        console.log(`Successfully copied selected text: "${copiedText.substring(0, 50)}${copiedText.length > 50 ? '...' : ''}"`);
        return { selectedText: copiedText, originalClipboard };
      } else {
        console.log('No text was selected or copy operation failed');
        // Restore original clipboard since no text was copied
        clipboard.writeText(originalClipboard);
        return { selectedText: null, originalClipboard };
      }
    } catch (error) {
      console.error('Failed to copy selected text with preservation:', error);
      return { selectedText: null, originalClipboard: clipboard.readText() };
    }
  }

  // Method to restore clipboard content
  restoreClipboard(originalContent: string): void {
    try {
      clipboard.writeText(originalContent);
      console.log('Clipboard content restored');
    } catch (error) {
      console.error('Failed to restore clipboard content:', error);
    }
  }

  // Method to copy selected text while keeping it selected for replacement
  async copySelectedTextForReplacement(): Promise<{ selectedText: string | null; originalClipboard: string }> {
    try {
      console.log('Copying selected text for replacement...');

      // Store original clipboard content
      const originalClipboard = clipboard.readText();

      // Strategy: Copy the text but DON'T try to recreate selection
      // Instead, we'll rely on the fact that most applications keep text selected
      // after a copy operation, and we'll paste immediately to replace it

      // Clear clipboard to detect if copy operation succeeds
      clipboard.writeText('');

      // Wait a moment for clipboard to clear
      await this.sleep(50);

      // Simulate copy operation based on platform
      await this.simulateCopy();

      // Wait for copy operation to complete
      await this.sleep(150);

      // Read the clipboard to see if text was copied
      const copiedText = clipboard.readText();

      // Check if we actually copied something
      if (copiedText && copiedText.trim().length > 0) {
        console.log(`Successfully copied selected text for replacement: "${copiedText.substring(0, 50)}${copiedText.length > 50 ? '...' : ''}"`);

        // DON'T try to reselect - many applications maintain selection after copy
        // We'll handle the replacement in the replaceSelectedText method

        return { selectedText: copiedText, originalClipboard };
      } else {
        console.log('No text was selected or copy operation failed');
        // Restore original clipboard since no text was copied
        clipboard.writeText(originalClipboard);
        return { selectedText: null, originalClipboard };
      }
    } catch (error) {
      console.error('Failed to copy selected text for replacement:', error);
      return { selectedText: null, originalClipboard: clipboard.readText() };
    }
  }



  // Method to replace selected text with new content
  async replaceSelectedText(newText: string, originalClipboard: string, originalText: string): Promise<boolean> {
    try {
      console.log(`Replacing selected text with: "${newText.substring(0, 50)}${newText.length > 50 ? '...' : ''}"`);

      // Strategy: Use a more robust replacement approach
      // 1. First, try to reselect the original text using find functionality
      // 2. If that fails, fall back to simple paste (which works if selection is maintained)

      // Put the original text in clipboard temporarily to find and select it
      clipboard.writeText(originalText);
      await this.sleep(50);

      // Try to find and select the original text
      const selectionRestored = await this.findAndSelectOriginalText();

      if (!selectionRestored) {
        console.log('Could not restore selection via find, trying direct selection approach...');
        // Fallback: Try to select the text using keyboard shortcuts
        await this.attemptDirectSelection(originalText);
      }

      // Now set the new text to clipboard
      clipboard.writeText(newText);
      await this.sleep(50);

      // Paste to replace the selected text
      await this.simulatePaste();

      // Restore original clipboard content after a delay
      setTimeout(() => {
        clipboard.writeText(originalClipboard);
        console.log('Original clipboard content restored after replacement');
      }, 1500);

      return true;
    } catch (error) {
      console.error('Failed to replace selected text:', error);
      // Restore original clipboard on error
      clipboard.writeText(originalClipboard);
      return false;
    }
  }

  // Method to find and select the original text using Ctrl+F
  private async findAndSelectOriginalText(): Promise<boolean> {
    try {
      console.log('Attempting to find and select original text...');

      switch (this.platform) {
        case 'win32':
          return await this.findAndSelectWindows();
        case 'darwin':
          return await this.findAndSelectMacOS();
        case 'linux':
          return await this.findAndSelectLinux();
        default:
          return false;
      }
    } catch (error) {
      console.warn('Failed to find and select text:', error);
      return false;
    }
  }

  private async findAndSelectWindows(): Promise<boolean> {
    try {
      // Open find dialog, paste the text to find it, then close dialog
      const command = `
        Add-Type -AssemblyName System.Windows.Forms
        [System.Windows.Forms.SendKeys]::SendWait("^f")
        Start-Sleep -Milliseconds 300
        [System.Windows.Forms.SendKeys]::SendWait("^v")
        Start-Sleep -Milliseconds 100
        [System.Windows.Forms.SendKeys]::SendWait("{ENTER}")
        Start-Sleep -Milliseconds 100
        [System.Windows.Forms.SendKeys]::SendWait("{ESCAPE}")
      `;
      await execAsync(`powershell -Command "${command}"`);
      return true;
    } catch (error) {
      return false;
    }
  }

  private async findAndSelectMacOS(): Promise<boolean> {
    try {
      // Open find dialog, paste the text to find it, then close dialog
      const command = `osascript -e '
        tell application "System Events"
          keystroke "f" using command down
          delay 0.3
          keystroke "v" using command down
          delay 0.1
          key code 36
          delay 0.1
          key code 53
        end tell'`;
      await execAsync(command);
      return true;
    } catch (error) {
      return false;
    }
  }

  private async findAndSelectLinux(): Promise<boolean> {
    try {
      await execAsync('which xdotool');
      await execAsync('xdotool key ctrl+f');
      await this.sleep(300);
      await execAsync('xdotool key ctrl+v');
      await this.sleep(100);
      await execAsync('xdotool key Return');
      await this.sleep(100);
      await execAsync('xdotool key Escape');
      return true;
    } catch (error) {
      return false;
    }
  }

  // Fallback method to attempt direct selection
  private async attemptDirectSelection(originalText: string): Promise<void> {
    try {
      console.log('Attempting direct selection fallback...');

      // Strategy: Use Ctrl+A to select all, then immediately paste
      // This works well in single-line inputs or when the entire content should be replaced
      if (originalText.length < 200 && !originalText.includes('\n')) {
        await this.selectAllText();
      }
    } catch (error) {
      console.warn('Direct selection fallback failed:', error);
    }
  }

  private async selectAllText(): Promise<void> {
    switch (this.platform) {
      case 'win32':
        const command = `
          Add-Type -AssemblyName System.Windows.Forms
          [System.Windows.Forms.SendKeys]::SendWait("^a")
        `;
        await execAsync(`powershell -Command "${command}"`);
        break;
      case 'darwin':
        const macCommand = `osascript -e 'tell application "System Events" to keystroke "a" using command down'`;
        await execAsync(macCommand);
        break;
      case 'linux':
        await execAsync('which xdotool');
        await execAsync('xdotool key ctrl+a');
        break;
    }
  }



  // Private method to simulate copy operation based on platform
  private async simulateCopy(): Promise<void> {
    try {
      switch (this.platform) {
        case 'win32':
          await this.simulateCopyWindows();
          break;
        case 'darwin':
          await this.simulateCopyMacOS();
          break;
        case 'linux':
          await this.simulateCopyLinux();
          break;
        default:
          throw new Error(`Unsupported platform: ${this.platform}`);
      }
    } catch (error) {
      console.error('Failed to simulate copy:', error);
      throw error;
    }
  }

  private async simulateCopyWindows(): Promise<void> {
    // On Windows, we can use PowerShell to send Ctrl+C
    const command = `
      Add-Type -AssemblyName System.Windows.Forms
      [System.Windows.Forms.SendKeys]::SendWait("^c")
    `;

    await execAsync(`powershell -Command "${command}"`);
  }

  private async simulateCopyMacOS(): Promise<void> {
    // On macOS, we can use osascript to send Cmd+C
    const command = `osascript -e 'tell application "System Events" to keystroke "c" using command down'`;
    await execAsync(command);
  }

  private async simulateCopyLinux(): Promise<void> {
    // On Linux, we can use xdotool to send Ctrl+C
    // First check if xdotool is available
    try {
      await execAsync('which xdotool');
      await execAsync('xdotool key ctrl+c');
    } catch (error) {
      // Fallback to xclip + xdotool alternative
      try {
        await execAsync('which xclip');
        await execAsync('xdotool key ctrl+c');
      } catch (fallbackError) {
        console.warn('Neither xdotool nor xclip found. Text copying may not work properly.');
        throw new Error('Required tools (xdotool or xclip) not found for text copying on Linux');
      }
    }
  }



  // Method to check if text insertion is supported on current platform
  async isSupported(): Promise<boolean> {
    try {
      switch (this.platform) {
        case 'win32':
          // Check if PowerShell is available
          await execAsync('powershell -Command "Get-Host"');
          return true;
          
        case 'darwin':
          // Check if osascript is available
          await execAsync('which osascript');
          return true;
          
        case 'linux':
          // Check if xdotool is available
          try {
            await execAsync('which xdotool');
            return true;
          } catch {
            // Check for xclip as fallback
            await execAsync('which xclip');
            return true;
          }
          
        default:
          return false;
      }
    } catch (error) {
      console.warn('Text insertion support check failed:', error);
      return false;
    }
  }

  // Method to install required dependencies on Linux
  async installLinuxDependencies(): Promise<string> {
    if (this.platform !== 'linux') {
      return 'Not applicable on this platform';
    }

    const instructions = `
To enable text insertion on Linux, please install one of the following:

Option 1 - xdotool (recommended):
  Ubuntu/Debian: sudo apt-get install xdotool
  Fedora/RHEL:   sudo dnf install xdotool
  Arch:          sudo pacman -S xdotool

Option 2 - xclip:
  Ubuntu/Debian: sudo apt-get install xclip
  Fedora/RHEL:   sudo dnf install xclip
  Arch:          sudo pacman -S xclip

After installation, restart the application.
    `;

    return instructions.trim();
  }

  // Method to get platform-specific requirements
  getPlatformRequirements(): string {
    switch (this.platform) {
      case 'win32':
        return 'Windows: PowerShell (built-in)';
      case 'darwin':
        return 'macOS: osascript (built-in)';
      case 'linux':
        return 'Linux: xdotool or xclip (install via package manager)';
      default:
        return 'Platform not supported';
    }
  }
}

// Text insertion implementation notes:
//
// This implementation uses a clipboard-based approach which is:
// 1. Cross-platform compatible
// 2. Doesn't require additional native dependencies
// 3. Works with most applications
//
// For a more robust implementation, consider:
//
// 1. Using robotjs for direct keyboard simulation:
//    - More reliable than clipboard method
//    - Requires native compilation
//    - May need additional system permissions
//
// 2. Platform-specific solutions:
//    - Windows: SendInput API via native addon
//    - macOS: CGEventCreateKeyboardEvent via native addon
//    - Linux: X11 or Wayland APIs via native addon
//
// 3. Accessibility APIs:
//    - Windows: UI Automation
//    - macOS: Accessibility API
//    - Linux: AT-SPI
//
// 4. Security considerations:
//    - Some applications may block programmatic input
//    - Antivirus software may flag keyboard simulation
//    - User permissions may be required for accessibility features
