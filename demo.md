# WhisperTyping Clone Demo

## Quick Start Demo

This demo will help you test the basic functionality of the WhisperTyping Clone application.

### 1. Application Launch

The application should be running in the background. You'll see:
- A system tray icon (blue square)
- Console output showing "Global shortcut registered: CommandOrControl+Shift+Space"

### 2. Opening the Interface

**Method 1**: Click the system tray icon
**Method 2**: Press `Ctrl+Shift+Space` to trigger the hotkey (this will also start recording)

### 3. Testing Speech Recognition (Demo Mode)

Since no OpenAI API key is configured initially, the app runs in demo mode:

1. Press and hold `Ctrl+Shift+Space`
2. The status should change to "Recording..."
3. The tray icon should turn red (recording state)
4. Release the hotkey
5. You should see:
   - Status changes to "Processing..."
   - A mock transcription appears in the "Last Transcription" area
   - The mock text gets "typed" into the active application (via clipboard)

### 4. Testing the UI

1. Click the tray icon to open the main window
2. Click "Settings" to expand the configuration section
3. Try entering a test API key (format: sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx)
4. Change the language setting
5. Click "Save Configuration"

### 5. Expected Behavior

**Without API Key (Demo Mode)**:
- Recording works (collects audio data)
- Returns mock transcription: "Mock transcription of X bytes of audio data..."
- Text insertion works (copies mock text to clipboard and pastes)

**With Valid API Key**:
- Recording works
- Audio sent to OpenAI Whisper API
- Real transcription returned
- Real text inserted into active application

### 6. Testing Text Insertion

1. Open a text editor (like gedit, notepad, or any text field)
2. Click in the text field to make it active
3. Press `Ctrl+Shift+Space` in the WhisperTyping app
4. The mock transcription should appear in the text editor

### 7. Platform-Specific Notes

**Linux**:
- Requires SoX for audio recording: `sudo apt-get install sox`
- Requires xdotool for text insertion: `sudo apt-get install xdotool`
- May need to grant microphone permissions

**macOS**:
- May prompt for microphone permissions
- May prompt for accessibility permissions for text insertion

**Windows**:
- Should work out of the box
- May prompt for microphone permissions

### 8. Troubleshooting

**Audio Recording Issues**:
```bash
# Test if SoX is working (Linux)
rec test.wav

# Check microphone permissions
# Linux: Check PulseAudio/ALSA settings
# macOS: System Preferences > Security & Privacy > Microphone
# Windows: Settings > Privacy > Microphone
```

**Text Insertion Issues**:
```bash
# Test xdotool (Linux)
xdotool type "Hello World"

# Test clipboard access
echo "test" | xclip -selection clipboard  # Linux
pbcopy <<< "test"  # macOS
```

**Global Hotkey Issues**:
- Check if another application is using the same hotkey
- Try changing the hotkey in settings
- Restart the application after changing hotkeys

### 9. Development Testing

**Console Output to Monitor**:
```
Global shortcut registered: CommandOrControl+Shift+Space
Starting audio recording...
Audio recording started
Stopping audio recording...
Audio recording stopped. Captured X bytes
No API key configured, returning mock transcription
Inserting text: "Mock transcription of X bytes..."
```

**File Locations**:
- Config file: `~/.config/whisper-typing-clone/config.json` (Linux)
- Logs: Check the Electron console output
- Temp files: `/tmp/whisper-recording-*.wav`

### 10. Next Steps

1. **Get OpenAI API Key**: Visit https://platform.openai.com/api-keys
2. **Configure Real API**: Enter your API key in the settings
3. **Test Real Transcription**: Try recording actual speech
4. **Customize Settings**: Adjust language, hotkeys, etc.
5. **Build for Distribution**: Run `npm run dist` to create installers

### 11. Demo Script

Here's a simple test sequence:

1. Start the app: `npm run dev`
2. Verify tray icon appears
3. Open a text editor
4. Press `Ctrl+Shift+Space` for 2 seconds
5. Check that mock text appears in the editor
6. Open app settings and configure API key
7. Test with real speech
8. Verify real transcription works

### 12. Performance Metrics

**Expected Performance**:
- Startup time: < 3 seconds
- Recording start latency: < 500ms
- Mock transcription: < 100ms
- Real transcription: 2-5 seconds (depends on audio length and API response)
- Text insertion: < 200ms

**Resource Usage**:
- Memory: ~50-100MB (Electron app)
- CPU: Low when idle, moderate during recording/transcription
- Network: Only when using OpenAI API

This demo should help you verify that all core components are working correctly!
