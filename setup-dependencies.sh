#!/bin/bash

# Setup script for WhisperTyping Clone dependencies

echo "Setting up dependencies for WhisperTyping Clone..."

# Detect the operating system
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    echo "Detected Linux system"
    
    # Check if we're on Ubuntu/Debian
    if command -v apt-get &> /dev/null; then
        echo "Installing dependencies with apt-get..."
        echo "You may be prompted for your password."
        
        # Update package list
        sudo apt-get update
        
        # Install SoX for audio recording
        echo "Installing SoX (audio recording)..."
        sudo apt-get install -y sox
        
        # Install xdotool for text insertion
        echo "Installing xdotool (text insertion)..."
        sudo apt-get install -y xdotool
        
        # Install xclip as backup for text insertion
        echo "Installing xclip (clipboard access)..."
        sudo apt-get install -y xclip
        
    # Check if we're on Fedora/RHEL
    elif command -v dnf &> /dev/null; then
        echo "Installing dependencies with dnf..."
        echo "You may be prompted for your password."
        
        # Install SoX for audio recording
        echo "Installing SoX (audio recording)..."
        sudo dnf install -y sox
        
        # Install xdotool for text insertion
        echo "Installing xdotool (text insertion)..."
        sudo dnf install -y xdotool
        
        # Install xclip as backup for text insertion
        echo "Installing xclip (clipboard access)..."
        sudo dnf install -y xclip
        
    # Check if we're on Arch
    elif command -v pacman &> /dev/null; then
        echo "Installing dependencies with pacman..."
        echo "You may be prompted for your password."
        
        # Install SoX for audio recording
        echo "Installing SoX (audio recording)..."
        sudo pacman -S --noconfirm sox
        
        # Install xdotool for text insertion
        echo "Installing xdotool (text insertion)..."
        sudo pacman -S --noconfirm xdotool
        
        # Install xclip as backup for text insertion
        echo "Installing xclip (clipboard access)..."
        sudo pacman -S --noconfirm xclip
        
    else
        echo "Unsupported Linux distribution. Please install manually:"
        echo "- sox (for audio recording)"
        echo "- xdotool (for text insertion)"
        echo "- xclip (for clipboard access)"
        exit 1
    fi
    
elif [[ "$OSTYPE" == "darwin"* ]]; then
    echo "Detected macOS system"
    
    # Check if Homebrew is installed
    if command -v brew &> /dev/null; then
        echo "Installing dependencies with Homebrew..."
        
        # Install SoX for audio recording
        echo "Installing SoX (audio recording)..."
        brew install sox
        
        echo "macOS dependencies installed successfully!"
        echo "Note: You may need to grant microphone and accessibility permissions."
        
    else
        echo "Homebrew not found. Please install Homebrew first:"
        echo "https://brew.sh/"
        echo "Then run: brew install sox"
        exit 1
    fi
    
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    echo "Detected Windows system"
    echo "Windows dependencies should be handled automatically by the application."
    echo "If you encounter issues, please install:"
    echo "- SoX for Windows: https://sourceforge.net/projects/sox/"
    
else
    echo "Unsupported operating system: $OSTYPE"
    exit 1
fi

echo ""
echo "Dependency installation completed!"
echo ""
echo "Testing installed dependencies..."

# Test SoX
if command -v sox &> /dev/null; then
    echo "✓ SoX is installed and available"
    sox --version | head -1
else
    echo "✗ SoX is not available"
fi

# Test xdotool (Linux only)
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if command -v xdotool &> /dev/null; then
        echo "✓ xdotool is installed and available"
        xdotool version
    else
        echo "✗ xdotool is not available"
    fi
    
    if command -v xclip &> /dev/null; then
        echo "✓ xclip is installed and available"
        xclip -version
    else
        echo "✗ xclip is not available"
    fi
fi

echo ""
echo "Setup complete! You can now run the application with:"
echo "npm run dev"
echo ""
echo "If you encounter permission issues:"
echo "- Linux: Grant microphone permissions and ensure your user is in the 'audio' group"
echo "- macOS: Grant microphone and accessibility permissions in System Preferences"
echo "- Windows: Grant microphone permissions in Privacy settings"
