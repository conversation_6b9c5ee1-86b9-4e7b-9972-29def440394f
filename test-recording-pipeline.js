#!/usr/bin/env node

/**
 * Test script to audit the current recording→upload pipeline
 * 
 * This script will:
 * 1. Simulate AudioRecorder.start() creating raw LPCM stream
 * 2. Capture Buffer.concat(audioChunks) without WAV header
 * 3. Package buffer into FormData and attempt to send to OpenAI
 * 4. Capture sample buffer details and OpenAI error
 */

const fs = require('fs');
const FormData = require('form-data');
const axios = require('axios');

// Mock audio data - simulating raw LPCM16 format
// 16 kHz sample rate, 16-bit samples, mono channel
const SAMPLE_RATE = 16000;
const BITS_PER_SAMPLE = 16;
const CHANNELS = 1;
const DURATION_SECONDS = 3; // 3 seconds of audio

console.log('🎯 STEP 1: Simulating AudioRecorder.start() - Creating raw LPCM stream');
console.log('Parameters:');
console.log(`  - Sample Rate: ${SAMPLE_RATE} Hz`);
console.log(`  - Bits Per Sample: ${BITS_PER_SAMPLE}`);
console.log(`  - Channels: ${CHANNELS}`);
console.log(`  - Duration: ${DURATION_SECONDS} seconds`);

// Calculate buffer size for raw PCM
const bytesPerSample = BITS_PER_SAMPLE / 8;
const totalSamples = SAMPLE_RATE * DURATION_SECONDS * CHANNELS;
const bufferSize = totalSamples * bytesPerSample;

console.log(`  - Expected buffer size: ${bufferSize} bytes`);

// Create mock raw PCM data (simulating node-record-lpcm16 output)
const rawPCMBuffer = Buffer.alloc(bufferSize);

// Fill with simple sine wave pattern to simulate audio
for (let i = 0; i < totalSamples; i++) {
    const frequency = 440; // A4 note
    const amplitude = 16384; // Half of 16-bit range
    const sample = Math.sin(2 * Math.PI * frequency * i / SAMPLE_RATE) * amplitude;
    const sampleInt = Math.round(sample);
    rawPCMBuffer.writeInt16LE(sampleInt, i * bytesPerSample);
}

console.log('\n🎯 STEP 2: Simulating AudioRecorder.stop() - Raw PCM without WAV header');
console.log('AudioRecorder.stop() currently returns:', {
    type: 'Buffer',
    length: rawPCMBuffer.length,
    hasWAVHeader: false,
    format: 'Raw LPCM16'
});

// Save raw PCM to file for inspection
fs.writeFileSync('/home/<USER>/Documents/voice/sample-raw-pcm.bin', rawPCMBuffer);
console.log('✅ Raw PCM buffer saved to sample-raw-pcm.bin');

console.log('\n🎯 STEP 3: Simulating speech/recognizer.ts - Packaging into FormData');

// This is exactly what the current recognizer.ts does
const formData = new FormData();
formData.append('file', rawPCMBuffer, {
    filename: 'audio.wav',  // ← This is the problem! We're claiming it's WAV but it's raw PCM
    contentType: 'audio/wav'
});
formData.append('model', 'whisper-1');
formData.append('response_format', 'text');

console.log('FormData created with:');
console.log('  - filename: "audio.wav" (INCORRECT - this is raw PCM!)');
console.log('  - contentType: "audio/wav" (INCORRECT - this is raw PCM!)');
console.log('  - model: "whisper-1"');
console.log('  - response_format: "text"');

console.log('\n🎯 STEP 4: Attempting to send to OpenAI API (will fail)');

// Check if API key is available
const apiKey = process.env.OPENAI_API_KEY;
if (!apiKey) {
    console.log('❌ No OPENAI_API_KEY environment variable found');
    console.log('📝 SIMULATING API request with raw PCM data...');
    simulateOpenAIError();
} else {
    console.log('✅ OPENAI_API_KEY found, attempting real API call...');
    attemptRealAPICall(formData, apiKey);
}

function simulateOpenAIError() {
    console.log('\n🔍 ANALYSIS: Expected OpenAI API Error');
    console.log('The OpenAI Whisper API expects one of these formats:');
    console.log('  - WAV (with proper RIFF header)');
    console.log('  - MP3');
    console.log('  - FLAC');
    console.log('  - M4A');
    console.log('  - OGG');
    console.log('  - WEBM');
    console.log('  - MP4');
    
    console.log('\n❌ Current issue: We\'re sending raw LPCM data claiming it\'s WAV');
    console.log('Raw LPCM structure:');
    console.log('  [sample1][sample2][sample3]...[sampleN]');
    console.log('  No file header, no metadata, just raw audio samples');
    
    console.log('\nWAV file structure should be:');
    console.log('  [RIFF Header][fmt chunk][data chunk][PCM samples]');
    console.log('  44 bytes of header + PCM data');
    
    console.log('\n📊 CAPTURED BUFFER DETAILS:');
    console.log(`  - Buffer length: ${rawPCMBuffer.length} bytes`);
    console.log(`  - Sample rate: ${SAMPLE_RATE} Hz`);
    console.log(`  - Bits per sample: ${BITS_PER_SAMPLE}`);
    console.log(`  - Channels: ${CHANNELS}`);
    console.log(`  - Duration: ${DURATION_SECONDS} seconds`);
    console.log(`  - Format: Raw LPCM16 (NOT WAV)`);
    console.log(`  - First 16 bytes: ${rawPCMBuffer.slice(0, 16).toString('hex')}`);
    
    console.log('\n🔧 SOLUTION: Add WAV header to raw PCM data before sending to OpenAI');
}

async function attemptRealAPICall(formData, apiKey) {
    try {
        console.log('🚀 Making request to OpenAI API...');
        const response = await axios.post('https://api.openai.com/v1/audio/transcriptions', formData, {
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                ...formData.getHeaders()
            },
            timeout: 30000
        });
        
        console.log('❌ UNEXPECTED: API call succeeded!');
        console.log('Response:', response.data);
        
    } catch (error) {
        console.log('✅ EXPECTED: API call failed as predicted');
        console.log('\n🔍 OpenAI API Error Details:');
        
        if (error.response) {
            console.log(`  - Status: ${error.response.status}`);
            console.log(`  - Status Text: ${error.response.statusText}`);
            console.log(`  - Error Data:`, error.response.data);
            
            if (error.response.data?.error) {
                console.log(`  - Error Type: ${error.response.data.error.type}`);
                console.log(`  - Error Message: ${error.response.data.error.message}`);
                console.log(`  - Error Code: ${error.response.data.error.code}`);
            }
        } else if (error.request) {
            console.log('  - Network Error: No response received');
        } else {
            console.log('  - Error:', error.message);
        }
        
        console.log('\n📊 CAPTURED BUFFER DETAILS:');
        console.log(`  - Buffer length: ${rawPCMBuffer.length} bytes`);
        console.log(`  - Sample rate: ${SAMPLE_RATE} Hz`);
        console.log(`  - Bits per sample: ${BITS_PER_SAMPLE}`);
        console.log(`  - Channels: ${CHANNELS}`);
        console.log(`  - Duration: ${DURATION_SECONDS} seconds`);
        console.log(`  - Format: Raw LPCM16 (NOT WAV)`);
        console.log(`  - First 16 bytes: ${rawPCMBuffer.slice(0, 16).toString('hex')}`);
        
        console.log('\n🔧 ROOT CAUSE CONFIRMED:');
        console.log('The OpenAI API is rejecting our raw PCM data because:');
        console.log('1. We claim it\'s a WAV file (filename: "audio.wav")');
        console.log('2. We set Content-Type: "audio/wav"');
        console.log('3. But the actual data is raw LPCM without WAV headers');
        console.log('4. OpenAI\'s audio parser expects valid WAV format with RIFF headers');
    }
}

// Create a proper WAV file for comparison
function createWAVFile(pcmBuffer, filename) {
    const wavHeader = Buffer.alloc(44);
    
    // RIFF header
    wavHeader.write('RIFF', 0);
    wavHeader.writeUInt32LE(36 + pcmBuffer.length, 4);
    wavHeader.write('WAVE', 8);
    
    // fmt chunk
    wavHeader.write('fmt ', 12);
    wavHeader.writeUInt32LE(16, 16); // fmt chunk size
    wavHeader.writeUInt16LE(1, 20);  // PCM format
    wavHeader.writeUInt16LE(CHANNELS, 22);
    wavHeader.writeUInt32LE(SAMPLE_RATE, 24);
    wavHeader.writeUInt32LE(SAMPLE_RATE * CHANNELS * bytesPerSample, 28); // byte rate
    wavHeader.writeUInt16LE(CHANNELS * bytesPerSample, 32); // block align
    wavHeader.writeUInt16LE(BITS_PER_SAMPLE, 34);
    
    // data chunk
    wavHeader.write('data', 36);
    wavHeader.writeUInt32LE(pcmBuffer.length, 40);
    
    const wavBuffer = Buffer.concat([wavHeader, pcmBuffer]);
    fs.writeFileSync(filename, wavBuffer);
    
    console.log(`\n✅ Created proper WAV file: ${filename}`);
    console.log(`  - Total size: ${wavBuffer.length} bytes`);
    console.log(`  - Header size: 44 bytes`);
    console.log(`  - PCM data size: ${pcmBuffer.length} bytes`);
    console.log(`  - First 16 bytes: ${wavBuffer.slice(0, 16).toString('hex')}`);
    
    return wavBuffer;
}

// Create a proper WAV file for comparison
const properWAVBuffer = createWAVFile(rawPCMBuffer, '/home/<USER>/Documents/voice/sample-proper.wav');

console.log('\n🎯 PIPELINE AUDIT COMPLETE');
console.log('=====================================');
console.log('FINDINGS:');
console.log('1. ✅ AudioRecorder.start() correctly creates raw LPCM stream via node-record-lpcm16');
console.log('2. ✅ AudioRecorder.stop() correctly returns Buffer.concat(audioChunks) (raw PCM)');
console.log('3. ❌ speech/recognizer.ts incorrectly packages raw PCM as WAV in FormData');
console.log('4. ❌ OpenAI API rejects the data because it\'s not valid WAV format');
console.log('');
console.log('SOLUTION: Convert raw PCM to proper WAV format before sending to OpenAI');
console.log('FILES CREATED:');
console.log('  - sample-raw-pcm.bin (raw PCM data from AudioRecorder)');
console.log('  - sample-proper.wav (properly formatted WAV file)');
