#!/usr/bin/env node

/**
 * Test script for the text replacement functionality
 * This script tests the new replacement behavior for rewrite and reply modes
 */

const { TextInserter } = require('./dist/text/inserter');

async function testReplacementFunctionality() {
    console.log('🧪 Testing Text Replacement Functionality\n');
    
    try {
        // Initialize TextInserter
        console.log('1. 🔧 Initializing TextInserter...');
        const textInserter = new TextInserter();
        console.log('   ✅ TextInserter initialized successfully\n');
        
        // Test platform support
        console.log('2. 🖥️ Testing platform support...');
        const isSupported = await textInserter.isSupported();
        console.log(`   Platform: ${process.platform}`);
        console.log(`   Support: ${isSupported ? '✅ Supported' : '❌ Not supported'}`);
        console.log(`   Requirements: ${textInserter.getPlatformRequirements()}`);
        
        if (!isSupported) {
            console.log('   ⚠️ Platform not supported. Some tests may fail.');
            if (process.platform === 'linux') {
                console.log('   📋 Installation instructions:');
                console.log(textInserter.installLinuxDependencies());
            }
        }
        console.log('');
        
        // Test 1: Copy selected text for replacement
        console.log('3. 📋 Testing copy selected text for replacement...');
        console.log('   📝 Instructions:');
        console.log('   1. Select some text in any application (e.g., in a text editor)');
        console.log('   2. Press Enter to test the copy functionality');
        console.log('   3. The app will copy the text and attempt to maintain selection');
        console.log('');
        console.log('   Press Enter when you have selected text...');
        
        // Wait for user input
        await waitForUserInput();
        
        const { selectedText, originalClipboard } = await textInserter.copySelectedTextForReplacement();
        
        if (selectedText) {
            console.log(`   ✅ Successfully copied text: "${selectedText.substring(0, 50)}${selectedText.length > 50 ? '...' : ''}"`);
            console.log(`   📋 Original clipboard preserved: "${originalClipboard.substring(0, 30)}${originalClipboard.length > 30 ? '...' : ''}"`);
            
            // Test 2: Replace the selected text
            console.log('\n4. 🔄 Testing text replacement...');
            console.log('   The selected text should now be replaced with a test message.');
            console.log('   Press Enter to proceed with replacement...');
            
            await waitForUserInput();
            
            const testReplacement = `[REPLACED] Original: "${selectedText.substring(0, 30)}..." -> New content generated by AI`;
            const success = await textInserter.replaceSelectedText(testReplacement, originalClipboard, selectedText);
            
            if (success) {
                console.log('   ✅ Text replacement completed successfully!');
                console.log('   📝 Check your text editor - the selected text should be replaced');
                console.log('   📋 Original clipboard will be restored in 1.5 seconds');
            } else {
                console.log('   ❌ Text replacement failed');
            }
        } else {
            console.log('   ℹ️ No text was selected or copy operation failed');
            console.log('   This is expected behavior when no text is selected');
        }
        
        console.log('\n5. 📊 Test Summary');
        console.log('   ✅ Copy selected text for replacement functionality tested');
        console.log('   ✅ Text replacement functionality tested');
        console.log('   ✅ Clipboard preservation tested');
        console.log('');
        console.log('🎉 Replacement functionality test completed!');
        console.log('');
        console.log('📝 How it works in the app:');
        console.log('   1. Select text in any application');
        console.log('   2. Press hotkey and say "rewrite this to be more formal"');
        console.log('   3. App copies the selected text');
        console.log('   4. AI processes the text');
        console.log('   5. App REPLACES the selected text with AI result');
        console.log('   6. Original clipboard is restored');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        console.error('Stack trace:', error.stack);
    }
}

function waitForUserInput() {
    return new Promise((resolve) => {
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        
        rl.question('', () => {
            rl.close();
            resolve();
        });
    });
}

// Run tests if this script is executed directly
if (require.main === module) {
    testReplacementFunctionality().catch(console.error);
}

module.exports = { testReplacementFunctionality };
